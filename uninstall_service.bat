@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - Windows服务卸载
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 需要管理员权限才能卸载Windows服务
    echo 请右键点击此批处理文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

:: 停止服务（如果正在运行）
echo.
echo [1/2] 停止服务...
net stop SecurityScanService >nul 2>&1
if errorlevel 1 (
    echo ⚠️  服务可能未运行或已停止
) else (
    echo ✅ 服务已停止
)

:: 卸载服务
echo.
echo [2/2] 卸载服务...
python windows_service.py remove
if errorlevel 1 (
    echo ❌ 服务卸载失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Windows服务卸载完成！
echo ========================================
pause
