#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全扫描系统测试脚本
Security Scan System Test Script

这个脚本用于测试安全扫描系统的完整功能
This script tests the complete functionality of the security scan system
"""

import os
import sys
import json
import subprocess
import platform
from datetime import datetime

def print_header():
    """打印测试脚本头部信息"""
    print("=" * 60)
    print("🔒 安全扫描系统测试脚本")
    print("🔒 Security Scan System Test Script")
    print("=" * 60)
    print(f"📅 测试时间 / Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💻 操作系统 / OS: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本 / Python Version: {sys.version}")
    print("=" * 60)
    print()

def check_python_environment():
    """检查Python环境"""
    print("📦 [1/4] 检查Python环境 / Checking Python Environment...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        print("❌ Python version too old, requires 3.7+")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} 环境正常")
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} environment OK")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📚 [2/4] 检查依赖包 / Checking Dependencies...")
    
    required_packages = [
        'schedule',
        'smtplib',  # 内置模块
        'email',    # 内置模块
        'json',     # 内置模块
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package in ['smtplib', 'email', 'json']:
                # 这些是内置模块，直接导入测试
                __import__(package)
            else:
                # 第三方包
                __import__(package)
            print(f"✅ {package} 模块可用")
        except ImportError:
            print(f"❌ {package} 模块缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("\n💡 请运行以下命令安装:")
        print("💡 Please run the following command to install:")
        if platform.system() == "Windows":
            print("   install.bat")
        else:
            print(f"   pip3 install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    print("✅ All dependencies check passed")
    return True

def check_configuration():
    """检查配置文件"""
    print("\n⚙️ [3/4] 检查配置文件 / Checking Configuration...")
    
    config_file = "scan_config.json"
    
    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print(f"❌ Configuration file not found: {config_file}")
        print("\n🔧 正在创建默认配置文件...")
        print("🔧 Creating default configuration file...")
        
        try:
            # 运行配置管理器创建配置文件
            result = subprocess.run([sys.executable, "config_manager.py"], 
                                  capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                print("✅ 默认配置文件已创建")
                print("✅ Default configuration file created")
            else:
                print("❌ 创建配置文件失败")
                print("❌ Failed to create configuration file")
                return False
        except Exception as e:
            print(f"❌ 创建配置文件时出错: {e}")
            print(f"❌ Error creating configuration file: {e}")
            return False
        
        print("\n⚠️ 请编辑配置文件后重新运行测试")
        print("⚠️ Please edit the configuration file and run the test again")
        return False
    
    # 检查配置文件内容
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查SMTP配置
        smtp_config = config.get('smtp', {})
        if smtp_config.get('username') == '<EMAIL>':
            print("⚠️ 检测到默认邮箱配置")
            print("⚠️ Default email configuration detected")
            print("📝 请修改 scan_config.json 中的SMTP设置")
            print("📝 Please modify SMTP settings in scan_config.json")
            return False
        
        # 检查收件人配置
        email_config = config.get('email', {})
        recipients = email_config.get('recipients', [])
        if not recipients or '<EMAIL>' in recipients:
            print("⚠️ 检测到默认收件人配置")
            print("⚠️ Default recipient configuration detected")
            print("📝 请修改 scan_config.json 中的收件人设置")
            print("📝 Please modify recipient settings in scan_config.json")
            return False
        
        # 检查扫描目标
        scan_config = config.get('scan', {})
        target_hosts = scan_config.get('target_hosts', [])
        if not target_hosts:
            print("⚠️ 未配置扫描目标")
            print("⚠️ No scan targets configured")
            return False
        
        print("✅ 配置文件检查通过")
        print("✅ Configuration file check passed")
        print(f"📧 邮件发送至: {', '.join(recipients)}")
        print(f"📧 Email will be sent to: {', '.join(recipients)}")
        print(f"🎯 扫描目标: {', '.join(target_hosts)}")
        print(f"🎯 Scan targets: {', '.join(target_hosts)}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        print(f"❌ Configuration file JSON format error: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        print(f"❌ Failed to read configuration file: {e}")
        return False

def run_test_scan():
    """执行测试扫描"""
    print("\n🔍 [4/4] 执行测试扫描 / Running Test Scan...")
    print("这将执行一次完整的扫描和邮件发送测试")
    print("This will perform a complete scan and email sending test")
    print()
    
    # 确认执行
    try:
        confirm = input("确认执行测试？/ Confirm to execute test? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是', '确认']:
            print("测试已取消 / Test cancelled")
            return True
    except KeyboardInterrupt:
        print("\n测试已取消 / Test cancelled")
        return True
    
    print("\n🚀 开始测试扫描...")
    print("🚀 Starting test scan...")
    
    try:
        # 执行扫描
        result = subprocess.run([
            sys.executable, 
            "security_scan_scheduler.py", 
            "-m", "once"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ 测试扫描执行完成")
            print("✅ Test scan execution completed")
            return True
        else:
            print(f"\n❌ 测试扫描执行失败，退出码: {result.returncode}")
            print(f"❌ Test scan execution failed, exit code: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ 执行测试扫描时出错: {e}")
        print(f"❌ Error during test scan execution: {e}")
        return False

def print_results():
    """打印测试结果和检查项"""
    print("\n" + "=" * 60)
    print("📊 测试完成！/ Test Completed!")
    print("=" * 60)
    print("请检查以下项目 / Please check the following items:")
    print()
    print("1. 📺 控制台输出是否正常")
    print("   Console output is normal")
    print()
    print("2. 📁 logs/ 目录下的日志文件")
    print("   Log files in logs/ directory")
    print("   - scan.log (扫描日志)")
    print("   - email.log (邮件日志)")
    print("   - scheduler.log (调度日志)")
    print()
    print("3. 📄 reports/ 目录下的报告文件")
    print("   Report files in reports/ directory")
    print("   - HTML格式报告")
    print("   - 文本格式报告")
    print()
    print("4. 📧 是否收到邮件报告")
    print("   Email reports received")
    print()
    print("💡 如果遇到问题，请检查日志文件获取详细信息")
    print("💡 If you encounter issues, please check log files for details")
    print("=" * 60)

def main():
    """主函数"""
    print_header()
    
    # 执行各项检查
    checks = [
        ("Python环境", check_python_environment),
        ("依赖包", check_dependencies),
        ("配置文件", check_configuration),
        ("测试扫描", run_test_scan),
    ]
    
    for check_name, check_func in checks:
        if not check_func():
            print(f"\n❌ {check_name}检查失败，测试终止")
            print(f"❌ {check_name} check failed, test terminated")
            return False
    
    # 打印结果
    print_results()
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断 / Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期的错误: {e}")
        print(f"❌ Unexpected error during test: {e}")
        sys.exit(1)
