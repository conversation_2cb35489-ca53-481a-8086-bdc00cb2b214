@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - Windows 安装脚本
echo ========================================
echo.

:: 检查Python版本
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.10+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

:: 检查pip
echo.
echo [2/5] 检查pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: pip未安装
    pause
    exit /b 1
)
echo ✅ pip已安装

:: 安装依赖
echo.
echo [3/5] 安装Python依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 错误: 依赖包安装失败
    pause
    exit /b 1
)
echo ✅ 依赖包安装完成

:: 创建必要目录
echo.
echo [4/5] 创建目录结构...
if not exist "logs" mkdir logs
if not exist "reports" mkdir reports
echo ✅ 目录创建完成

:: 初始化配置
echo.
echo [5/5] 初始化配置文件...
python start_scan_service.py
echo ✅ 初始化完成

echo.
echo ========================================
echo 🎉 安装完成！
echo.
echo 下一步操作：
echo 1. 编辑 scan_config.json 配置文件
echo 2. 运行 test.bat 进行测试
echo 3. 运行 start_service.bat 启动服务
echo ========================================
pause
