#!/usr/bin/env python3
# windows_service.py - Windows服务包装器

import sys
import os
import time
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False
    print("警告: pywin32未安装，无法注册为Windows服务")
    print("可以使用批处理脚本手动运行")

from security_scan_scheduler import SecurityScanService

class SecurityScanWindowsService(win32serviceutil.ServiceFramework):
    """安全扫描Windows服务"""
    
    _svc_name_ = "SecurityScanService"
    _svc_display_name_ = "安全扫描定时任务服务"
    _svc_description_ = "自动化安全扫描系统，定期执行端口扫描并发送邮件报告"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_running = True
        
        # 设置工作目录
        os.chdir(current_dir)
        
        # 设置日志
        self.setup_service_logging()
        
    def setup_service_logging(self):
        """设置服务日志"""
        log_file = current_dir / "logs" / "windows_service.log"
        log_file.parent.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def SvcStop(self):
        """停止服务"""
        self.logger.info("收到停止服务请求")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        
        # 停止扫描服务
        if hasattr(self, 'scan_service'):
            try:
                self.scan_service.stop_scheduler()
            except Exception as e:
                self.logger.error(f"停止扫描服务失败: {e}")
    
    def SvcDoRun(self):
        """运行服务"""
        try:
            self.logger.info("安全扫描Windows服务启动")
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
            
            # 初始化扫描服务
            config_file = current_dir / "scan_config.json"
            if not config_file.exists():
                self.logger.error(f"配置文件不存在: {config_file}")
                return
            
            self.scan_service = SecurityScanService(str(config_file))
            
            # 在单独线程中启动调度器
            import threading
            scheduler_thread = threading.Thread(target=self._run_scheduler)
            scheduler_thread.daemon = True
            scheduler_thread.start()
            
            # 等待停止信号
            while self.is_running:
                rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                if rc == win32event.WAIT_OBJECT_0:
                    break
            
            self.logger.info("安全扫描Windows服务停止")
            
        except Exception as e:
            self.logger.error(f"服务运行错误: {e}")
            servicemanager.LogErrorMsg(f"服务运行错误: {e}")
    
    def _run_scheduler(self):
        """运行调度器"""
        try:
            self.scan_service.start_scheduler()
        except Exception as e:
            self.logger.error(f"调度器运行错误: {e}")

def install_service():
    """安装服务"""
    if not PYWIN32_AVAILABLE:
        print("错误: 需要安装pywin32才能注册Windows服务")
        print("请运行: pip install pywin32")
        return False
    
    try:
        win32serviceutil.InstallService(
            SecurityScanWindowsService,
            SecurityScanWindowsService._svc_name_,
            SecurityScanWindowsService._svc_display_name_,
            description=SecurityScanWindowsService._svc_description_
        )
        print(f"✅ 服务 '{SecurityScanWindowsService._svc_display_name_}' 安装成功")
        return True
    except Exception as e:
        print(f"❌ 服务安装失败: {e}")
        return False

def remove_service():
    """卸载服务"""
    if not PYWIN32_AVAILABLE:
        print("错误: 需要安装pywin32才能操作Windows服务")
        return False
    
    try:
        win32serviceutil.RemoveService(SecurityScanWindowsService._svc_name_)
        print(f"✅ 服务 '{SecurityScanWindowsService._svc_display_name_}' 卸载成功")
        return True
    except Exception as e:
        print(f"❌ 服务卸载失败: {e}")
        return False

if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 作为服务运行
        if PYWIN32_AVAILABLE:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(SecurityScanWindowsService)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            print("pywin32未安装，无法作为Windows服务运行")
            print("请使用批处理脚本运行")
    else:
        # 命令行操作
        command = sys.argv[1].lower()
        if command == 'install':
            install_service()
        elif command == 'remove':
            remove_service()
        elif command == 'start':
            if PYWIN32_AVAILABLE:
                win32serviceutil.StartService(SecurityScanWindowsService._svc_name_)
                print("✅ 服务启动成功")
            else:
                print("pywin32未安装")
        elif command == 'stop':
            if PYWIN32_AVAILABLE:
                win32serviceutil.StopService(SecurityScanWindowsService._svc_name_)
                print("✅ 服务停止成功")
            else:
                print("pywin32未安装")
        else:
            print("用法:")
            print("  python windows_service.py install   - 安装服务")
            print("  python windows_service.py remove    - 卸载服务")
            print("  python windows_service.py start     - 启动服务")
            print("  python windows_service.py stop      - 停止服务")
