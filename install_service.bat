@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - Windows服务安装
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 需要管理员权限才能安装Windows服务
    echo 请右键点击此批处理文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

:: 检查Python环境
echo.
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 安装pywin32（如果需要）
echo.
echo [2/4] 检查pywin32模块...
python -c "import win32serviceutil" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pywin32未安装，正在安装...
    python -m pip install pywin32
    if errorlevel 1 (
        echo ❌ pywin32安装失败
        pause
        exit /b 1
    )
    echo ✅ pywin32安装完成
) else (
    echo ✅ pywin32已安装
)

:: 检查配置文件
echo.
echo [3/4] 检查配置文件...
if not exist "scan_config.json" (
    echo ❌ 错误: 配置文件不存在
    echo 请先运行 install.bat 进行初始化
    pause
    exit /b 1
)
echo ✅ 配置文件存在

:: 安装服务
echo.
echo [4/4] 安装Windows服务...
python windows_service.py install
if errorlevel 1 (
    echo ❌ 服务安装失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 Windows服务安装完成！
echo.
echo 服务名称: SecurityScanService
echo 显示名称: 安全扫描定时任务服务
echo.
echo 管理命令:
echo - 启动服务: net start SecurityScanService
echo - 停止服务: net stop SecurityScanService
echo - 或使用: python windows_service.py start/stop
echo.
echo 您也可以在"服务"管理器中管理此服务
echo ========================================
pause
