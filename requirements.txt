# 安全扫描系统依赖包
# 适用于 Windows Server 2012 + Python 3.10.9

# 核心依赖
schedule==1.2.0

# 可选依赖（如果需要更高级功能）
# APScheduler==3.10.4  # 高级任务调度（可选，如果需要更复杂的调度功能）

# 系统依赖（Python标准库，无需安装）
# socket - 网络连接
# datetime - 日期时间处理  
# json - JSON数据处理
# os - 操作系统接口
# sys - 系统相关参数
# logging - 日志记录
# argparse - 命令行参数解析
# time - 时间相关功能
# typing - 类型提示
# dataclasses - 数据类
# smtplib - SMTP邮件发送
# email - 邮件处理
# threading - 多线程
# pathlib - 路径处理
