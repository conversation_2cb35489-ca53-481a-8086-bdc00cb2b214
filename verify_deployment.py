#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全扫描系统部署验证脚本
Security Scan System Deployment Verification Script

这个脚本用于验证安全扫描系统是否正确部署和配置
This script verifies if the security scan system is properly deployed and configured
"""

import os
import sys
import json
import importlib
from datetime import datetime

def print_header():
    """打印验证脚本头部信息"""
    print("=" * 70)
    print("🔍 安全扫描系统部署验证")
    print("🔍 Security Scan System Deployment Verification")
    print("=" * 70)
    print(f"📅 验证时间 / Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    print()

def check_files():
    """检查必要文件是否存在"""
    print("📁 检查必要文件 / Checking Required Files...")
    
    required_files = [
        'config_manager.py',
        'email_sender.py',
        'logger_config.py',
        'report_generator.py',
        'scheduler.py',
        'security_scan.py',
        'security_scan_scheduler.py',
        'start_scan_service.py',
        'test_system.py',
        'requirements.txt'
    ]
    
    optional_files = [
        'scan_config.json',
        'install.bat',
        'start_service.bat',
        'stop_service.bat',
        'status.bat'
    ]
    
    missing_required = []
    missing_optional = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (必需文件)")
            missing_required.append(file)
    
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} (可选文件)")
            missing_optional.append(file)
    
    if missing_required:
        print(f"\n❌ 缺少必需文件: {', '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n⚠️ 缺少可选文件: {', '.join(missing_optional)}")
    
    print("\n✅ 文件检查通过")
    return True

def check_directories():
    """检查必要目录是否存在"""
    print("\n📂 检查目录结构 / Checking Directory Structure...")
    
    required_dirs = ['logs', 'reports']
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✅ {dir_name}/ 目录存在")
        else:
            print(f"⚠️ {dir_name}/ 目录不存在，正在创建...")
            try:
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ {dir_name}/ 目录已创建")
            except Exception as e:
                print(f"❌ 创建 {dir_name}/ 目录失败: {e}")
                return False
    
    print("✅ 目录结构检查通过")
    return True

def check_python_modules():
    """检查Python模块是否可以正常导入"""
    print("\n🐍 检查Python模块 / Checking Python Modules...")
    
    modules_to_check = [
        ('config_manager', 'ConfigManager'),
        ('email_sender', 'EmailSender'),
        ('logger_config', 'setup_logging_from_config'),
        ('report_generator', 'ReportGenerator'),
        ('scheduler', 'SchedulerManager'),
        ('security_scan', 'check_port'),
        ('security_scan_scheduler', 'SecurityScanService')
    ]
    
    failed_imports = []
    
    for module_name, class_or_func in modules_to_check:
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_or_func):
                print(f"✅ {module_name}.{class_or_func}")
            else:
                print(f"❌ {module_name}.{class_or_func} 不存在")
                failed_imports.append(f"{module_name}.{class_or_func}")
        except ImportError as e:
            print(f"❌ {module_name} 导入失败: {e}")
            failed_imports.append(module_name)
        except Exception as e:
            print(f"❌ {module_name} 检查失败: {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ 模块导入失败: {', '.join(failed_imports)}")
        return False
    
    print("\n✅ Python模块检查通过")
    return True

def check_configuration():
    """检查配置文件"""
    print("\n⚙️ 检查配置 / Checking Configuration...")
    
    config_file = "scan_config.json"
    
    if not os.path.exists(config_file):
        print(f"⚠️ 配置文件不存在: {config_file}")
        print("💡 请运行 python config_manager.py 创建配置文件")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查配置结构
        required_sections = ['smtp', 'scan', 'schedule', 'email', 'logging']
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节 {section} 存在")
            else:
                print(f"❌ 配置节 {section} 缺失")
                return False
        
        # 检查关键配置项
        smtp_config = config.get('smtp', {})
        if smtp_config.get('username') and smtp_config.get('password'):
            if smtp_config.get('username') != '<EMAIL>':
                print("✅ SMTP配置已设置")
            else:
                print("⚠️ SMTP配置使用默认值，需要修改")
        else:
            print("❌ SMTP配置不完整")
            return False
        
        # 检查扫描目标
        scan_config = config.get('scan', {})
        target_hosts = scan_config.get('target_hosts', [])
        if target_hosts:
            print(f"✅ 扫描目标已配置: {len(target_hosts)} 个主机")
        else:
            print("⚠️ 未配置扫描目标")
        
        # 检查收件人
        email_config = config.get('email', {})
        recipients = email_config.get('recipients', [])
        if recipients and '<EMAIL>' not in recipients:
            print(f"✅ 邮件收件人已配置: {len(recipients)} 个")
        else:
            print("⚠️ 邮件收件人使用默认值，需要修改")
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包 / Checking Dependencies...")
    
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except Exception as e:
        print(f"❌ 读取requirements.txt失败: {e}")
        return False
    
    missing_packages = []
    
    for requirement in requirements:
        package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0]
        try:
            importlib.import_module(package_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("💡 请运行 pip install -r requirements.txt 安装依赖")
        return False
    
    print("\n✅ 依赖包检查通过")
    return True

def print_summary(results):
    """打印验证结果摘要"""
    print("\n" + "=" * 70)
    print("📊 验证结果摘要 / Verification Summary")
    print("=" * 70)
    
    checks = [
        ("文件检查", "File Check"),
        ("目录检查", "Directory Check"),
        ("模块检查", "Module Check"),
        ("配置检查", "Configuration Check"),
        ("依赖检查", "Dependencies Check")
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (check_cn, check_en) in enumerate(checks):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"{check_cn} / {check_en}: {status}")
    
    print("-" * 70)
    print(f"总体结果 / Overall Result: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 系统部署验证成功！")
        print("🎉 System deployment verification successful!")
        print("\n💡 您现在可以运行以下命令启动系统:")
        print("💡 You can now run the following commands to start the system:")
        print("   - python test_system.py (测试系统)")
        print("   - python security_scan_scheduler.py -m once (单次扫描)")
        print("   - python security_scan_scheduler.py -m daemon (启动定时服务)")
    else:
        print("⚠️ 系统部署验证未完全通过")
        print("⚠️ System deployment verification not fully passed")
        print("\n💡 请根据上述检查结果修复问题后重新验证")
        print("💡 Please fix the issues based on the check results and verify again")
    
    print("=" * 70)

def main():
    """主函数"""
    print_header()
    
    # 执行各项检查
    checks = [
        check_files,
        check_directories,
        check_python_modules,
        check_configuration,
        check_dependencies
    ]
    
    results = []
    
    for check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            results.append(False)
    
    # 打印摘要
    print_summary(results)
    
    return all(results)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 验证被用户中断 / Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 验证过程中发生未预期的错误: {e}")
        print(f"❌ Unexpected error during verification: {e}")
        sys.exit(1)
