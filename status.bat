@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - 状态检查
echo ========================================
echo.

:: 检查配置文件
echo [1] 配置文件检查:
if exist "scan_config.json" (
    echo ✅ scan_config.json 存在
) else (
    echo ❌ scan_config.json 不存在
)

:: 检查日志目录
echo.
echo [2] 目录结构检查:
if exist "logs" (
    echo ✅ logs 目录存在
) else (
    echo ❌ logs 目录不存在
)

if exist "reports" (
    echo ✅ reports 目录存在
) else (
    echo ❌ reports 目录不存在
)

:: 检查PID文件
echo.
echo [3] 服务状态检查:
if exist "scheduler.pid" (
    set /p PID=<scheduler.pid
    echo 📋 PID文件存在: %PID%
    
    :: 检查进程是否真的在运行
    tasklist /PID %PID% >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  PID %PID% 对应的进程不存在（可能是僵尸PID文件）
    ) else (
        echo ✅ 服务正在运行 (PID: %PID%)
    )
) else (
    echo ❌ 未找到PID文件，服务未运行
)

:: 检查Python进程
echo.
echo [4] Python进程检查:
tasklist /FI "IMAGENAME eq python.exe" /FO CSV | findstr "security_scan" >nul
if errorlevel 1 (
    echo ❌ 未发现相关Python进程
) else (
    echo ✅ 发现相关Python进程
    echo 相关进程列表:
    tasklist /FI "IMAGENAME eq python.exe" /FO TABLE
)

:: 使用Python检查状态
echo.
echo [5] 系统状态详情:
python security_scan_scheduler.py -m status 2>nul
if errorlevel 1 (
    echo ⚠️  无法获取详细状态信息
)

echo.
echo ========================================
pause
