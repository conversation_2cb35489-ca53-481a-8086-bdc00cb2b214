@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - 停止服务
echo ========================================
echo.

:: 检查PID文件
if not exist "scheduler.pid" (
    echo ⚠️  未找到PID文件，服务可能未运行
    goto :check_process
)

:: 读取PID
set /p PID=<scheduler.pid
echo 📋 找到PID: %PID%

:: 尝试终止进程
echo 🛑 正在停止服务...
taskkill /PID %PID% /F >nul 2>&1
if errorlevel 1 (
    echo ⚠️  无法通过PID停止进程，尝试按进程名停止...
    goto :check_process
) else (
    echo ✅ 服务已停止
    del scheduler.pid >nul 2>&1
    goto :end
)

:check_process
echo 🔍 检查相关Python进程...
tasklist /FI "IMAGENAME eq python.exe" /FO CSV | findstr "security_scan_scheduler" >nul
if errorlevel 1 (
    echo ✅ 未发现运行中的扫描服务
) else (
    echo ⚠️  发现相关进程，尝试停止...
    taskkill /IM python.exe /FI "WINDOWTITLE eq security_scan_scheduler*" /F >nul 2>&1
    echo ✅ 已尝试停止相关进程
)

:end
echo.
echo 操作完成
pause
