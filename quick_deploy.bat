@echo off
chcp 65001 >nul
title 安全扫描系统 - 快速部署向导

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    安全扫描系统                              ║
echo ║                   快速部署向导                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ 需要管理员权限才能完成部署
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo ✅ 管理员权限确认
echo.

:: 步骤1：环境检查和安装
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 步骤 1/5: 环境检查和依赖安装                                │
echo └─────────────────────────────────────────────────────────────┘
call install.bat
if errorlevel 1 (
    echo ❌ 环境安装失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 步骤 2/5: 部署验证                                          │
echo └─────────────────────────────────────────────────────────────┘
python test_deployment.py
if errorlevel 1 (
    echo ❌ 部署验证失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 步骤 3/5: 配置文件编辑                                      │
echo └─────────────────────────────────────────────────────────────┘
echo.
echo 📝 现在需要编辑配置文件 scan_config.json
echo.
echo 主要配置项：
echo   1. SMTP邮箱设置 (smtp 部分)
echo   2. 扫描目标主机 (scan.target_hosts)
echo   3. 收件人邮箱 (email.recipients)
echo   4. 定时设置 (schedule 部分)
echo.
set /p edit_config="是否现在打开配置文件进行编辑？(Y/n): "
if /i not "%edit_config%"=="n" (
    notepad scan_config.json
    echo.
    echo 请保存配置文件后按任意键继续...
    pause >nul
)

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 步骤 4/5: 功能测试                                          │
echo └─────────────────────────────────────────────────────────────┘
echo.
set /p run_test="是否执行功能测试？(Y/n): "
if /i not "%run_test%"=="n" (
    call test.bat
)

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │ 步骤 5/5: 选择运行方式                                      │
echo └─────────────────────────────────────────────────────────────┘
echo.
echo 请选择运行方式：
echo   1. 批处理脚本运行 (适合测试和临时使用)
echo   2. Windows服务运行 (推荐用于生产环境)
echo   3. 稍后手动启动
echo.
set /p run_mode="请输入选择 (1-3): "

if "%run_mode%"=="1" (
    echo.
    echo 🚀 启动批处理服务...
    echo 注意：关闭此窗口将停止服务
    echo 按 Ctrl+C 可停止服务
    echo.
    pause
    call start_service.bat
) else if "%run_mode%"=="2" (
    echo.
    echo 🔧 安装Windows服务...
    call install_service.bat
    if not errorlevel 1 (
        echo.
        echo 🚀 启动Windows服务...
        net start SecurityScanService
        if not errorlevel 1 (
            echo ✅ Windows服务启动成功
        )
    )
) else (
    echo.
    echo 📋 手动启动说明：
    echo   批处理方式: start_service.bat
    echo   Windows服务: install_service.bat 然后 net start SecurityScanService
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      部署完成                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎉 安全扫描系统部署完成！
echo.
echo 📋 管理命令：
echo   检查状态: status.bat
echo   停止服务: stop_service.bat (批处理) 或 net stop SecurityScanService (服务)
echo   查看日志: type logs\security_scan.log
echo.
echo 📁 重要文件位置：
echo   配置文件: scan_config.json
echo   日志目录: logs\
echo   报告目录: reports\
echo.
echo 📧 系统将按配置的时间表自动执行扫描并发送邮件报告
echo.
pause
