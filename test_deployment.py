#!/usr/bin/env python3
# test_deployment.py - 部署验证测试脚本

import os
import sys
import json
import socket
import importlib
from pathlib import Path
from datetime import datetime

class DeploymentTester:
    """部署验证测试器"""
    
    def __init__(self):
        self.test_results = []
        self.current_dir = Path(__file__).parent.absolute()
        
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'status': status
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
        
    def test_python_version(self):
        """测试Python版本"""
        version = sys.version_info
        if version.major == 3 and version.minor >= 6:
            self.log_test("Python版本", True, f"Python {version.major}.{version.minor}.{version.micro}")
        else:
            self.log_test("Python版本", False, f"需要Python 3.6+，当前: {version.major}.{version.minor}.{version.micro}")
    
    def test_dependencies(self):
        """测试依赖包"""
        required_modules = ['schedule', 'socket', 'json', 'datetime', 'logging']
        
        for module in required_modules:
            try:
                importlib.import_module(module)
                self.log_test(f"模块 {module}", True, "已安装")
            except ImportError:
                self.log_test(f"模块 {module}", False, "未安装")
    
    def test_file_structure(self):
        """测试文件结构"""
        required_files = [
            'security_scan_scheduler.py',
            'config_manager.py',
            'email_sender.py',
            'report_generator.py',
            'scheduler.py',
            'logger_config.py',
            'security_scan.py',
            'start_scan_service.py'
        ]
        
        for file_name in required_files:
            file_path = self.current_dir / file_name
            if file_path.exists():
                self.log_test(f"文件 {file_name}", True, "存在")
            else:
                self.log_test(f"文件 {file_name}", False, "不存在")
    
    def test_directories(self):
        """测试目录结构"""
        required_dirs = ['logs', 'reports']
        
        for dir_name in required_dirs:
            dir_path = self.current_dir / dir_name
            if dir_path.exists() and dir_path.is_dir():
                self.log_test(f"目录 {dir_name}", True, "存在")
            else:
                # 尝试创建目录
                try:
                    dir_path.mkdir(exist_ok=True)
                    self.log_test(f"目录 {dir_name}", True, "已创建")
                except Exception as e:
                    self.log_test(f"目录 {dir_name}", False, f"创建失败: {e}")
    
    def test_config_file(self):
        """测试配置文件"""
        config_file = self.current_dir / "scan_config.json"
        
        if not config_file.exists():
            self.log_test("配置文件", False, "scan_config.json 不存在")
            return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必要的配置项
            required_sections = ['smtp', 'scan', 'email', 'schedule', 'logging']
            for section in required_sections:
                if section in config:
                    self.log_test(f"配置节 {section}", True, "存在")
                else:
                    self.log_test(f"配置节 {section}", False, "缺失")
            
            # 检查SMTP配置
            smtp_config = config.get('smtp', {})
            if smtp_config.get('username') and smtp_config.get('password'):
                if smtp_config.get('username') != '<EMAIL>':
                    self.log_test("SMTP配置", True, "已配置")
                else:
                    self.log_test("SMTP配置", False, "使用默认配置，需要修改")
            else:
                self.log_test("SMTP配置", False, "缺少用户名或密码")
            
            # 检查收件人配置
            recipients = config.get('email', {}).get('recipients', [])
            if recipients and '<EMAIL>' not in recipients:
                self.log_test("收件人配置", True, f"{len(recipients)} 个收件人")
            else:
                self.log_test("收件人配置", False, "使用默认配置，需要修改")
                
        except json.JSONDecodeError as e:
            self.log_test("配置文件", False, f"JSON格式错误: {e}")
        except Exception as e:
            self.log_test("配置文件", False, f"读取错误: {e}")
    
    def test_network_connectivity(self):
        """测试网络连接"""
        # 测试DNS解析
        try:
            socket.gethostbyname('www.google.com')
            self.log_test("DNS解析", True, "正常")
        except Exception as e:
            self.log_test("DNS解析", False, f"失败: {e}")
        
        # 测试SMTP连接（如果配置了）
        config_file = self.current_dir / "scan_config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                smtp_config = config.get('smtp', {})
                smtp_server = smtp_config.get('smtp_server')
                smtp_port = smtp_config.get('smtp_port', 587)
                
                if smtp_server:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((smtp_server, smtp_port))
                    sock.close()
                    
                    if result == 0:
                        self.log_test("SMTP连接", True, f"{smtp_server}:{smtp_port}")
                    else:
                        self.log_test("SMTP连接", False, f"无法连接到 {smtp_server}:{smtp_port}")
                        
            except Exception as e:
                self.log_test("SMTP连接", False, f"测试失败: {e}")
    
    def test_permissions(self):
        """测试文件权限"""
        # 测试日志目录写权限
        log_dir = self.current_dir / "logs"
        try:
            test_file = log_dir / "test_write.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()
            self.log_test("日志目录写权限", True, "正常")
        except Exception as e:
            self.log_test("日志目录写权限", False, f"失败: {e}")
        
        # 测试报告目录写权限
        report_dir = self.current_dir / "reports"
        try:
            test_file = report_dir / "test_write.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()
            self.log_test("报告目录写权限", True, "正常")
        except Exception as e:
            self.log_test("报告目录写权限", False, f"失败: {e}")
    
    def test_import_modules(self):
        """测试导入自定义模块"""
        custom_modules = [
            'config_manager',
            'email_sender', 
            'report_generator',
            'scheduler',
            'logger_config',
            'security_scan'
        ]
        
        for module in custom_modules:
            try:
                importlib.import_module(module)
                self.log_test(f"导入 {module}", True, "成功")
            except Exception as e:
                self.log_test(f"导入 {module}", False, f"失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🔍 安全扫描系统部署验证测试")
        print("=" * 60)
        print()
        
        print("📋 基础环境测试:")
        self.test_python_version()
        self.test_dependencies()
        print()
        
        print("📁 文件结构测试:")
        self.test_file_structure()
        self.test_directories()
        print()
        
        print("⚙️ 配置测试:")
        self.test_config_file()
        print()
        
        print("🌐 网络连接测试:")
        self.test_network_connectivity()
        print()
        
        print("🔐 权限测试:")
        self.test_permissions()
        print()
        
        print("📦 模块导入测试:")
        self.test_import_modules()
        print()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print("=" * 60)
        print("📊 测试结果统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        print("=" * 60)
        
        if failed_tests > 0:
            print("\n❌ 发现问题，请检查以下失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
            print("\n请解决这些问题后重新运行测试")
            return False
        else:
            print("\n🎉 所有测试通过！系统部署验证成功")
            print("您现在可以启动安全扫描服务了")
            return True

def main():
    """主函数"""
    tester = DeploymentTester()
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 部署验证完成 - 系统就绪")
        print("\n下一步操作:")
        print("1. 运行 test.bat 进行功能测试")
        print("2. 运行 start_service.bat 启动服务")
        print("3. 或安装为Windows服务: install_service.bat")
    else:
        print("❌ 部署验证失败 - 需要修复问题")
        print("\n建议操作:")
        print("1. 检查上述失败的测试项")
        print("2. 修复问题后重新运行此脚本")
        print("3. 如需帮助，请查看部署文档")
    print("=" * 60)

if __name__ == "__main__":
    main()
