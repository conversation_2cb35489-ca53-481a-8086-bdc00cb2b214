@echo off
:: 设置代码页为UTF-8以支持中文显示
chcp 65001 >nul 2>&1

:: 设置控制台字体（可选，某些系统可能需要）
if exist "%SystemRoot%\System32\chcp.com" (
    chcp 65001 >nul 2>&1
)

echo ========================================
echo Security Scan System - Test Script
echo 安全扫描系统 - 测试脚本
echo ========================================
echo.

:: 检查环境
echo [1/4] Environment Check / 环境检查...
python --version 2>nul
if errorlevel 1 (
    echo ERROR: Python not installed or not in PATH
    echo 错误: Python未安装或不在PATH中
    echo.
    echo Please install Python 3.7+ and add it to PATH
    echo 请安装Python 3.7+并添加到PATH环境变量
    pause
    exit /b 1
)
echo OK: Python environment ready
echo 成功: Python环境就绪

:: 检查依赖
echo.
echo [2/4] Dependencies Check / 依赖检查...
python -c "import schedule; print('OK: schedule module available')" 2>nul
if errorlevel 1 (
    echo ERROR: schedule module not installed
    echo 错误: schedule模块未安装
    echo.
    echo Please run: install.bat
    echo 请运行: install.bat
    pause
    exit /b 1
)

:: 检查配置
echo.
echo [3/4] Configuration Check / 配置检查...
if not exist "scan_config.json" (
    echo WARNING: Configuration file not found, creating...
    echo 警告: 配置文件不存在，正在创建...
    python start_scan_service.py
    echo.
    echo NOTICE: Please edit scan_config.json and run test again
    echo 提示: 请编辑 scan_config.json 文件后重新运行测试
    pause
    exit /b 1
)

:: 使用Python脚本检查配置，避免批处理中的中文问题
python -c "
import json
import sys

try:
    with open('scan_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)

    # 检查关键配置
    smtp = config.get('smtp', {})
    if smtp.get('username') == '<EMAIL>':
        print('WARNING: Default email configuration detected')
        print('警告: 检测到默认邮箱配置，请修改配置文件')
        sys.exit(1)

    recipients = config.get('email', {}).get('recipients', [])
    if not recipients or '<EMAIL>' in recipients:
        print('WARNING: Default recipient configuration detected')
        print('警告: 检测到默认收件人配置，请修改配置文件')
        sys.exit(1)

    print('OK: Configuration file format is correct')
    print('成功: 配置文件格式正确')
except Exception as e:
    print(f'ERROR: Configuration file error: {e}')
    print(f'错误: 配置文件错误: {e}')
    sys.exit(1)
"
if errorlevel 1 (
    echo.
    echo Please edit the configuration file and run test again
    echo 请编辑配置文件后重新运行测试
    pause
    exit /b 1
)

:: 执行测试扫描
echo.
echo [4/4] Execute Test Scan / 执行测试扫描...
echo This will perform a complete scan and email test
echo 这将执行一次完整的扫描和邮件发送测试
echo.
set /p confirm="Confirm to execute test? / 确认执行测试？(y/N): "
if /i not "%confirm%"=="y" (
    echo Test cancelled / 测试已取消
    pause
    exit /b 0
)

echo.
echo Starting test scan... / 开始测试扫描...
python security_scan_scheduler.py -m once

echo.
echo ========================================
echo Test completed! / 测试完成！
echo Please check: / 请检查：
echo 1. Console output / 控制台输出是否正常
echo 2. Log files in logs/ directory / logs/目录下的日志文件
echo 3. Report files in reports/ directory / reports/目录下的报告文件
echo 4. Email reports received / 是否收到邮件报告
echo ========================================
pause
