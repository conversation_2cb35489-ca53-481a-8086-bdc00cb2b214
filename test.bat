@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - 测试脚本
echo ========================================
echo.

:: 检查环境
echo [1/4] 环境检查...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

:: 检查依赖
echo.
echo [2/4] 依赖检查...
python -c "import schedule; print('✅ schedule模块正常')" 2>nul
if errorlevel 1 (
    echo ❌ schedule模块未安装，请运行 install.bat
    pause
    exit /b 1
)

:: 检查配置
echo.
echo [3/4] 配置检查...
if not exist "scan_config.json" (
    echo ❌ 配置文件不存在，正在创建...
    python start_scan_service.py
    echo.
    echo ⚠️  请编辑 scan_config.json 文件后重新运行测试
    pause
    exit /b 1
)

python -c "
import json
try:
    with open('scan_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 检查关键配置
    smtp = config.get('smtp', {})
    if smtp.get('username') == '<EMAIL>':
        print('⚠️  检测到默认邮箱配置，请修改配置文件')
        exit(1)
    
    recipients = config.get('email', {}).get('recipients', [])
    if not recipients or '<EMAIL>' in recipients:
        print('⚠️  检测到默认收件人配置，请修改配置文件')
        exit(1)
    
    print('✅ 配置文件格式正确')
except Exception as e:
    print(f'❌ 配置文件错误: {e}')
    exit(1)
"
if errorlevel 1 (
    echo.
    echo 请编辑配置文件后重新运行测试
    pause
    exit /b 1
)

:: 执行测试扫描
echo.
echo [4/4] 执行测试扫描...
echo 这将执行一次完整的扫描和邮件发送测试
echo.
set /p confirm="确认执行测试？(y/N): "
if /i not "%confirm%"=="y" (
    echo 测试已取消
    pause
    exit /b 0
)

echo.
echo 🔍 开始测试扫描...
python security_scan_scheduler.py -m once

echo.
echo ========================================
echo 测试完成！
echo 请检查：
echo 1. 控制台输出是否正常
echo 2. logs/ 目录下的日志文件
echo 3. reports/ 目录下的报告文件
echo 4. 是否收到邮件报告
echo ========================================
pause
