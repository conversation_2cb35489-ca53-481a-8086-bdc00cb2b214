# 安全扫描系统使用说明

## 📋 系统概述

这是一个自动化的安全扫描系统，能够定期扫描指定的服务器端口，检测安全风险，并通过邮件发送详细的安全报告。

### 🎯 主要功能

- **端口扫描**: 扫描指定主机的常见端口
- **安全分析**: 识别高风险和中风险端口
- **报告生成**: 生成HTML和文本格式的详细报告
- **邮件通知**: 自动发送扫描报告到指定邮箱
- **定时任务**: 支持定时自动扫描
- **日志记录**: 完整的操作日志记录

## 🚀 快速开始

### 1. 系统验证

首先验证系统是否正确部署：

```bash
# 验证部署
python verify_deployment.py

# 或者在Windows上
verify_deployment.py
```

### 2. 系统测试

运行完整的系统测试：

```bash
# Python版本（推荐）
python test_system.py

# Windows批处理版本
test_simple.bat
```

### 3. 配置文件

编辑 `scan_config.json` 文件，配置以下关键信息：

#### SMTP邮箱设置
```json
{
  "smtp": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your_app_password",
    "use_tls": true,
    "sender_name": "Security Scanner"
  }
}
```

#### 扫描目标设置
```json
{
  "scan": {
    "target_hosts": [
      "*************",
      "*********"
    ]
  }
}
```

#### 邮件收件人设置
```json
{
  "email": {
    "recipients": [
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

## 📖 使用方法

### 单次扫描

执行一次性扫描：

```bash
# 扫描配置文件中的所有主机
python security_scan_scheduler.py -m once

# 扫描指定主机
python security_scan_scheduler.py -m once -H *************
```

### 启动定时服务

启动后台定时扫描服务：

```bash
# 启动定时服务
python security_scan_scheduler.py -m daemon

# Windows服务方式（需要管理员权限）
install_service.bat
start_service.bat
```

### 查看系统状态

```bash
# 查看系统状态
python security_scan_scheduler.py -m status

# Windows批处理方式
status.bat
```

### 停止服务

```bash
# 停止Windows服务
stop_service.bat

# 卸载Windows服务
uninstall_service.bat
```

## 📁 文件结构

```
scanSecurity/
├── 核心文件/
│   ├── security_scan_scheduler.py  # 主控制脚本
│   ├── security_scan.py           # 端口扫描模块
│   ├── config_manager.py          # 配置管理
│   ├── email_sender.py            # 邮件发送
│   ├── report_generator.py        # 报告生成
│   ├── scheduler.py               # 任务调度
│   └── logger_config.py           # 日志配置
├── 配置文件/
│   ├── scan_config.json           # 主配置文件
│   └── requirements.txt           # Python依赖
├── 测试工具/
│   ├── test_system.py             # 系统测试脚本
│   ├── verify_deployment.py       # 部署验证脚本
│   ├── test_simple.bat            # 简化测试脚本
│   └── test.bat                   # 完整测试脚本
├── Windows工具/
│   ├── install.bat                # 依赖安装
│   ├── install_service.bat        # 服务安装
│   ├── start_service.bat          # 启动服务
│   ├── stop_service.bat           # 停止服务
│   ├── status.bat                 # 状态查看
│   └── uninstall_service.bat      # 卸载服务
├── 输出目录/
│   ├── logs/                      # 日志文件
│   └── reports/                   # 扫描报告
└── 文档/
    ├── README.md                  # 项目说明
    ├── 使用说明.md                # 本文件
    └── Windows_部署指南.md        # Windows部署指南
```

## 📊 报告说明

### 扫描报告内容

每次扫描会生成包含以下信息的报告：

1. **扫描统计**
   - 扫描时间和目标主机
   - 开放/关闭端口数量
   - 安全警告数量

2. **安全警告**
   - 高风险端口（如RDP、数据库等）
   - 中风险端口（如DNS、Web服务等）
   - 详细的风险说明

3. **端口详情**
   - 每个端口的状态
   - 端口服务说明
   - 风险等级标识

### 报告格式

- **HTML格式**: 适合在浏览器中查看，包含样式和格式
- **文本格式**: 适合在终端或文本编辑器中查看

## 🔧 高级配置

### 定时任务配置

```json
{
  "schedule": {
    "enabled": true,
    "schedule_type": "weekly",     // daily, interval, weekly
    "interval_hours": 24,          // 仅interval模式
    "cron_expression": "0 8 * * 1", // 仅weekly模式
    "timezone": "Asia/Shanghai"
  }
}
```

### 扫描配置

```json
{
  "scan": {
    "default_ports": [21, 22, 23, 53, 80, 443, ...],
    "timeout": 3,                  // 端口扫描超时时间
    "concurrent_scans": false,     // 是否并发扫描
    "max_threads": 10              // 最大线程数
  }
}
```

### 邮件配置

```json
{
  "email": {
    "subject_template": "🔒 安全扫描报告 - {host} - {date}",
    "include_attachments": true,   // 是否包含附件
    "max_retries": 3,             // 最大重试次数
    "send_on_no_issues": true,    // 无问题时是否发送
    "send_summary_only": false    // 是否只发送摘要
  }
}
```

## 🐛 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认邮箱密码或应用专用密码
   - 检查网络连接

2. **扫描失败**
   - 确认目标主机可达
   - 检查防火墙设置
   - 验证网络权限

3. **服务启动失败**
   - 检查Python环境
   - 确认依赖包已安装
   - 查看错误日志

### 日志文件

- `logs/scan.log`: 扫描操作日志
- `logs/email.log`: 邮件发送日志
- `logs/scheduler.log`: 调度任务日志
- `logs/*_error.log`: 各模块错误日志

### 获取帮助

1. 查看日志文件获取详细错误信息
2. 运行 `python verify_deployment.py` 检查系统状态
3. 运行 `python test_system.py` 进行完整测试

## 📝 注意事项

1. **权限要求**: 某些端口扫描可能需要管理员权限
2. **网络安全**: 请确保在授权的网络环境中使用
3. **邮件安全**: 建议使用应用专用密码而非账户密码
4. **定期更新**: 定期检查和更新扫描端口列表
5. **合规使用**: 请遵守相关法律法规和公司政策

## 🔄 更新日志

- **v1.0.0**: 初始版本，包含基本扫描和报告功能
- 支持Windows服务部署
- 完整的测试和验证工具
- 中英文双语支持
