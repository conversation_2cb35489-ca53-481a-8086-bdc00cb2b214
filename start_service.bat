@echo off
chcp 65001 >nul
echo ========================================
echo 安全扫描系统 - 启动服务
echo ========================================
echo.

:: 检查配置文件
if not exist "scan_config.json" (
    echo ❌ 错误: 配置文件 scan_config.json 不存在
    echo 请先运行 install.bat 进行初始化
    pause
    exit /b 1
)

:: 检查是否已经在运行
if exist "scheduler.pid" (
    echo ⚠️  检测到PID文件存在，服务可能已在运行
    echo 如果确认服务未运行，请删除 scheduler.pid 文件
    pause
)

echo 🚀 启动安全扫描定时服务...
echo 按 Ctrl+C 停止服务
echo.

:: 启动服务
python security_scan_scheduler.py -m daemon

echo.
echo 服务已停止
pause
