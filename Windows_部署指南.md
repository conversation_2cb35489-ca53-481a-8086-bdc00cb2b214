# 安全扫描系统 - Windows Server 2012 部署指南

## 📋 部署概述

本指南将帮助您在 Windows Server 2012 上部署安全扫描系统。系统已针对 Windows 环境进行了优化，提供了完整的批处理脚本和 Windows 服务支持。

## 🔧 系统要求

### 硬件要求
- CPU: 1 核心以上
- 内存: 512MB 以上
- 磁盘: 100MB 可用空间
- 网络: 能够访问目标主机和 SMTP 服务器

### 软件要求
- **Windows Server 2012** (已确认)
- **Python 3.10.9** (已安装)
- 网络连接（用于扫描和邮件发送）

## 📦 部署步骤

### 第一步：准备部署文件

1. **上传项目文件**
   ```
   将整个项目文件夹上传到服务器，建议路径：
   C:\SecurityScan\
   ```

2. **验证文件完整性**
   确保以下文件存在：
   ```
   ├── security_scan_scheduler.py    # 主程序
   ├── config_manager.py            # 配置管理
   ├── email_sender.py              # 邮件发送
   ├── report_generator.py          # 报告生成
   ├── scheduler.py                 # 任务调度
   ├── logger_config.py             # 日志配置
   ├── security_scan.py             # 扫描核心
   ├── start_scan_service.py        # 启动检查
   ├── scan_config.json             # 配置文件
   ├── requirements.txt             # 依赖列表
   ├── windows_service.py           # Windows服务
   ├── test_deployment.py           # 部署测试
   └── 批处理脚本/
       ├── install.bat              # 安装脚本
       ├── test.bat                 # 测试脚本
       ├── start_service.bat        # 启动服务
       ├── stop_service.bat         # 停止服务
       ├── status.bat               # 状态检查
       ├── install_service.bat      # 安装Windows服务
       └── uninstall_service.bat    # 卸载Windows服务
   ```

### 第二步：环境初始化

1. **打开命令提示符**
   - 以管理员身份运行 `cmd`
   - 切换到项目目录：`cd C:\SecurityScan`

2. **运行安装脚本**
   ```batch
   install.bat
   ```
   
   此脚本将：
   - 检查 Python 环境
   - 安装必要的依赖包
   - 创建目录结构
   - 初始化配置文件

### 第三步：配置系统

1. **编辑配置文件**
   打开 `scan_config.json`，修改以下关键配置：

   **SMTP 邮箱设置：**
   ```json
   {
     "smtp": {
       "smtp_server": "smtp.your-company.com",
       "smtp_port": 587,
       "username": "<EMAIL>",
       "password": "your_password",
       "use_tls": true,
       "sender_name": "安全扫描系统"
     }
   }
   ```

   **扫描目标设置：**
   ```json
   {
     "scan": {
       "target_hosts": [
         "*************",
         "*********"
       ]
     }
   }
   ```

   **收件人设置：**
   ```json
   {
     "email": {
       "recipients": [
         "<EMAIL>",
         "<EMAIL>"
       ]
     }
   }
   ```

   **定时设置：**
   ```json
   {
     "schedule": {
       "enabled": true,
       "schedule_type": "weekly",
       "cron_expression": "0 2 * * 1"
     }
   }
   ```

### 第四步：验证部署

1. **运行部署验证**
   ```batch
   python test_deployment.py
   ```
   
   此脚本将检查：
   - Python 环境
   - 依赖包
   - 文件结构
   - 配置文件
   - 网络连接
   - 文件权限

2. **运行功能测试**
   ```batch
   test.bat
   ```
   
   此脚本将执行一次完整的扫描测试

### 第五步：启动服务

#### 方式一：批处理脚本运行（推荐用于测试）

```batch
# 启动服务
start_service.bat

# 检查状态
status.bat

# 停止服务
stop_service.bat
```

#### 方式二：Windows 服务运行（推荐用于生产）

1. **安装 Windows 服务**
   ```batch
   # 以管理员身份运行
   install_service.bat
   ```

2. **管理服务**
   ```batch
   # 启动服务
   net start SecurityScanService
   
   # 停止服务
   net stop SecurityScanService
   
   # 或使用服务管理器
   services.msc
   ```

3. **卸载服务**（如需要）
   ```batch
   uninstall_service.bat
   ```

## 🔍 验证部署成功

### 检查项目

1. **服务状态**
   - 运行 `status.bat` 检查服务状态
   - 查看 `logs/` 目录下的日志文件

2. **扫描功能**
   - 检查 `reports/` 目录是否生成报告文件
   - 确认收到邮件报告

3. **日志记录**
   - `logs/security_scan.log` - 主日志
   - `logs/scan.log` - 扫描日志
   - `logs/email.log` - 邮件日志
   - `logs/scheduler.log` - 调度日志

## 🛠️ 常见问题解决

### 问题 1：依赖包安装失败
**解决方案：**
```batch
# 升级 pip
python -m pip install --upgrade pip

# 手动安装依赖
python -m pip install schedule
```

### 问题 2：邮件发送失败
**检查项目：**
- SMTP 服务器地址和端口
- 用户名和密码
- 网络连接
- 防火墙设置

### 问题 3：权限错误
**解决方案：**
- 确保以管理员身份运行
- 检查文件夹权限
- 确保 Python 有写入权限

### 问题 4：扫描超时
**解决方案：**
- 增加 `timeout` 配置值
- 检查网络连通性
- 减少并发扫描数量

## 📊 监控和维护

### 日志监控
```batch
# 查看实时日志
type logs\security_scan.log

# 查看错误日志
type logs\security_scan_error.log
```

### 定期维护
- 定期检查日志文件大小
- 清理旧的报告文件
- 更新扫描目标列表
- 验证邮件配置

## 🔒 安全建议

1. **配置文件安全**
   - 设置适当的文件权限
   - 不要在版本控制中包含密码

2. **网络安全**
   - 仅扫描授权的主机
   - 使用安全的 SMTP 连接

3. **系统安全**
   - 定期更新 Python 和依赖包
   - 监控系统资源使用

## 📞 技术支持

如遇到问题：
1. 查看日志文件获取详细错误信息
2. 运行 `test_deployment.py` 进行诊断
3. 检查网络连接和防火墙设置
4. 验证配置文件格式

## 📋 快速部署检查清单

### 部署前检查
- [ ] Windows Server 2012 系统正常运行
- [ ] Python 3.10.9 已安装并可正常使用
- [ ] 网络连接正常，可访问目标主机
- [ ] 有管理员权限
- [ ] 项目文件已完整上传

### 部署过程检查
- [ ] 运行 `install.bat` 成功
- [ ] 编辑 `scan_config.json` 配置文件
- [ ] 运行 `python test_deployment.py` 验证通过
- [ ] 运行 `test.bat` 功能测试成功
- [ ] 选择启动方式（批处理或Windows服务）

### 部署后验证
- [ ] 服务正常启动
- [ ] 日志文件正常生成
- [ ] 扫描报告正常生成
- [ ] 邮件报告正常发送
- [ ] 定时任务按计划执行

## 🚀 生产环境建议

### 性能优化
- 根据服务器性能调整 `max_threads` 参数
- 合理设置扫描超时时间
- 定期清理日志和报告文件

### 高可用性
- 配置多个 SMTP 服务器（备用）
- 设置多个收件人
- 监控服务运行状态

### 安全加固
- 使用专用服务账户运行
- 限制网络访问权限
- 定期更新系统和依赖

## 📈 扩展功能

### 自定义端口列表
在配置文件中添加特定端口：
```json
{
  "scan": {
    "default_ports": [21, 22, 23, 80, 443, 3389, 8080]
  }
}
```

### 多时间段扫描
配置不同的扫描计划：
```json
{
  "schedule": {
    "schedule_type": "daily",
    "cron_expression": "0 2,14 * * *"
  }
}
```

### 邮件模板自定义
修改邮件主题模板：
```json
{
  "email": {
    "subject_template": "[安全警报] {host} 扫描报告 - {date}"
  }
}
```

---

**🎉 部署完成后，系统将按照配置的时间表自动执行安全扫描并发送报告邮件。**

**如有任何问题，请参考日志文件或联系技术支持。**
